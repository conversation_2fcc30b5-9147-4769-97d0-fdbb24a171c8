#!/usr/bin/env python3
"""
Database migration script to add status and stop_monitor_date columns to uploaded_files table.
This script should be run once to update existing databases.
"""

import os
import sys
import logging
from sqlalchemy import create_engine, text, MetaData, Table, Column, String, Date
from config import Config

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def create_db_engine():
    """Create database engine"""
    try:
        engine = create_engine(
            Config.DB_URI,
            echo=False
        )
        logger.info("Database engine created successfully")
        return engine
    except Exception as e:
        logger.error(f"Error creating database engine: {str(e)}")
        raise

def check_column_exists(engine, table_name, column_name):
    """Check if a column exists in a table"""
    try:
        with engine.connect() as connection:
            # For SQLite
            if 'sqlite' in Config.DB_URI.lower():
                result = connection.execute(text(f"PRAGMA table_info({table_name})"))
                columns = [row[1] for row in result.fetchall()]
                return column_name in columns
            
            # For Oracle
            elif 'oracle' in Config.DB_URI.lower():
                result = connection.execute(text("""
                    SELECT column_name 
                    FROM user_tab_columns 
                    WHERE table_name = UPPER(:table_name) 
                    AND column_name = UPPER(:column_name)
                """), {"table_name": table_name, "column_name": column_name})
                return result.fetchone() is not None
            
            # For other databases (PostgreSQL, MySQL, etc.)
            else:
                result = connection.execute(text("""
                    SELECT column_name 
                    FROM information_schema.columns 
                    WHERE table_name = :table_name 
                    AND column_name = :column_name
                """), {"table_name": table_name, "column_name": column_name})
                return result.fetchone() is not None
                
    except Exception as e:
        logger.error(f"Error checking if column {column_name} exists in {table_name}: {str(e)}")
        return False

def add_status_column(engine):
    """Add status column to uploaded_files table"""
    try:
        with engine.begin() as connection:
            if 'sqlite' in Config.DB_URI.lower():
                # SQLite syntax
                connection.execute(text("ALTER TABLE uploaded_files ADD COLUMN status VARCHAR(50) DEFAULT 'idle'"))
            elif 'oracle' in Config.DB_URI.lower():
                # Oracle syntax
                connection.execute(text("ALTER TABLE uploaded_files ADD status VARCHAR2(50) DEFAULT 'idle'"))
            else:
                # Standard SQL syntax (PostgreSQL, MySQL, etc.)
                connection.execute(text("ALTER TABLE uploaded_files ADD COLUMN status VARCHAR(50) DEFAULT 'idle'"))
            
            logger.info("Successfully added status column to uploaded_files table")
            return True
    except Exception as e:
        logger.error(f"Error adding status column: {str(e)}")
        return False

def add_stop_monitor_date_column(engine):
    """Add stop_monitor_date column to uploaded_files table"""
    try:
        with engine.begin() as connection:
            if 'sqlite' in Config.DB_URI.lower():
                # SQLite syntax
                connection.execute(text("ALTER TABLE uploaded_files ADD COLUMN stop_monitor_date DATE"))
            elif 'oracle' in Config.DB_URI.lower():
                # Oracle syntax
                connection.execute(text("ALTER TABLE uploaded_files ADD stop_monitor_date DATE"))
            else:
                # Standard SQL syntax (PostgreSQL, MySQL, etc.)
                connection.execute(text("ALTER TABLE uploaded_files ADD COLUMN stop_monitor_date DATE"))
            
            logger.info("Successfully added stop_monitor_date column to uploaded_files table")
            return True
    except Exception as e:
        logger.error(f"Error adding stop_monitor_date column: {str(e)}")
        return False

def update_existing_records(engine):
    """Update existing records to have 'idle' status"""
    try:
        with engine.begin() as connection:
            result = connection.execute(text("UPDATE uploaded_files SET status = 'idle' WHERE status IS NULL"))
            logger.info(f"Updated {result.rowcount} records to have 'idle' status")
            return True
    except Exception as e:
        logger.error(f"Error updating existing records: {str(e)}")
        return False

def main():
    """Main migration function"""
    logger.info("Starting database migration to add status and stop_monitor_date columns")
    
    try:
        engine = create_db_engine()
        
        # Check if status column exists
        status_exists = check_column_exists(engine, 'uploaded_files', 'status')
        stop_monitor_exists = check_column_exists(engine, 'uploaded_files', 'stop_monitor_date')
        
        if status_exists:
            logger.info("Status column already exists in uploaded_files table")
        else:
            logger.info("Adding status column to uploaded_files table")
            if not add_status_column(engine):
                logger.error("Failed to add status column")
                return False
        
        if stop_monitor_exists:
            logger.info("Stop_monitor_date column already exists in uploaded_files table")
        else:
            logger.info("Adding stop_monitor_date column to uploaded_files table")
            if not add_stop_monitor_date_column(engine):
                logger.error("Failed to add stop_monitor_date column")
                return False
        
        # Update existing records to have 'idle' status
        if not status_exists:
            logger.info("Updating existing records to have 'idle' status")
            if not update_existing_records(engine):
                logger.error("Failed to update existing records")
                return False
        
        logger.info("Database migration completed successfully")
        return True
        
    except Exception as e:
        logger.error(f"Migration failed: {str(e)}")
        return False

if __name__ == "__main__":
    success = main()
    if success:
        print("Migration completed successfully!")
        sys.exit(0)
    else:
        print("Migration failed!")
        sys.exit(1)
