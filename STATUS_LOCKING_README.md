# Status Locking Implementation

This document describes the implementation of database-backed status locking to prevent concurrent status checks on the same files.

## Overview

The system now prevents multiple users from checking the status of the same file simultaneously. When a file's status is being checked, it's marked as "checking status" in the database, and any subsequent attempts to check the same file will be blocked with an appropriate error message.

## Changes Made

### 1. Database Schema Changes

**File: `Parts_Upload.py`**
- Added `status` column to `uploaded_files` table (VARCHAR(50), default: 'idle')
- Added `stop_monitor_date` column to `uploaded_files` table (DATE, nullable)

### 2. Status Management Functions

**File: `check_status.py`**
- `update_file_status(file_names, status)`: Update file status in database
- `check_files_status(file_names)`: Check if files are currently being processed
- `acquire_file_locks(file_names)`: Atomically acquire locks on files for processing
- `release_file_locks(file_names)`: Release locks by setting status back to 'idle'

### 3. Backend API Changes

**File: `app.py`**
- Modified `/status` endpoint to use database locking
- Updated `/get-file-status` endpoint to read from database
- Added proper error handling for concurrent access attempts

### 4. Frontend Changes

**File: `templates/index.html`**
- Updated status check handler to display appropriate error messages
- Added handling for HTTP 409 (Conflict) responses when files are already in progress

## Database Migration

For existing databases, run the migration script:

```bash
python migrate_add_status_column.py
```

This script will:
- Add the `status` column if it doesn't exist
- Add the `stop_monitor_date` column if it doesn't exist
- Set all existing records to 'idle' status

## How It Works

### Status Flow

1. **Idle State**: Files start in 'idle' status
2. **Lock Acquisition**: When status check is requested, system tries to acquire locks
3. **Processing State**: Successfully locked files are marked as 'checking_status'
4. **Status Check**: The actual status checking process runs
5. **Lock Release**: Files are returned to 'idle' status when complete

### Concurrent Access Prevention

1. User A selects files and clicks "Check Status"
2. System attempts to acquire locks on selected files
3. If successful, files are marked as 'checking_status' in database
4. User B tries to check status on same files
5. System detects files are already 'checking_status'
6. User B receives error: "File(s) {filename} already in progress"
7. When User A's check completes, files return to 'idle' status

### Error Handling

- **409 Conflict**: Returned when files are already being processed
- **500 Server Error**: Returned for other errors during status checking
- **Automatic Cleanup**: Locks are released even if errors occur

## Database Status Values

- `'idle'`: File is available for status checking
- `'checking_status'`: File is currently being processed
- `NULL`: Legacy records (treated as 'idle')

## Testing

Run the test script to verify functionality:

```bash
python test_status_locking.py
```

This will test:
- Basic lock acquisition and release
- Concurrent access prevention
- Multiple file locking
- Cleanup procedures

## API Endpoints

### POST /status
Check status of selected files with locking.

**Request:**
```json
{
    "files": ["file1.xlsx", "file2.xlsx"],
    "ignore_date": true
}
```

**Success Response (200):**
```json
{
    "status": "Success",
    "message": "Status updated successfully",
    "data": {...}
}
```

**Conflict Response (409):**
```json
{
    "status": "Error",
    "error": "File(s) file1.xlsx already in progress"
}
```

### GET /get-file-status
Get current status of all files.

**Response:**
```json
{
    "file1.xlsx": "Idle",
    "file2.xlsx": "In Progress",
    "file3.xlsx": "Idle"
}
```

## Backward Compatibility

The implementation maintains backward compatibility:
- Existing databases work with migration script
- In-memory status tracking is preserved for UI polling
- Frontend gracefully handles missing database columns

## Performance Considerations

- Database transactions ensure atomicity
- Minimal overhead for status checking
- Efficient queries using indexed file_name column
- Connection pooling prevents database bottlenecks

## Troubleshooting

### Files Stuck in "checking_status"

If files get stuck in processing state due to application crashes:

```python
from check_status import update_file_status
update_file_status(['stuck_file.xlsx'], 'idle')
```

### Migration Issues

If migration fails, manually add columns:

```sql
-- For SQLite
ALTER TABLE uploaded_files ADD COLUMN status VARCHAR(50) DEFAULT 'idle';
ALTER TABLE uploaded_files ADD COLUMN stop_monitor_date DATE;

-- For Oracle
ALTER TABLE uploaded_files ADD status VARCHAR2(50) DEFAULT 'idle';
ALTER TABLE uploaded_files ADD stop_monitor_date DATE;
```

## Future Enhancements

- Add timeout for stuck processes
- Implement process monitoring and auto-cleanup
- Add user identification for better conflict resolution
- Extend locking to other operations (upload, delete)
