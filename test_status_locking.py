#!/usr/bin/env python3
"""
Test script to verify the status locking functionality works correctly.
"""

import time
import threading
from check_status import acquire_file_locks, release_file_locks, check_files_status, update_file_status

def test_basic_locking():
    """Test basic file locking functionality"""
    print("Testing basic file locking...")
    
    test_files = ['test_file1.xlsx', 'test_file2.xlsx']
    
    # Test acquiring locks
    success, processing = acquire_file_locks(test_files)
    print(f"Lock acquisition: {success}, Processing files: {processing}")
    
    if success:
        # Test checking status
        status = check_files_status(test_files)
        print(f"Files in processing: {status}")
        
        # Release locks
        release_success = release_file_locks(test_files)
        print(f"Lock release: {release_success}")
        
        # Check status after release
        status_after = check_files_status(test_files)
        print(f"Files in processing after release: {status_after}")
    
    print("Basic locking test completed\n")

def test_concurrent_access():
    """Test concurrent access to the same files"""
    print("Testing concurrent access...")
    
    test_files = ['concurrent_test.xlsx']
    results = []
    
    def try_acquire_lock(thread_id):
        success, processing = acquire_file_locks(test_files)
        results.append((thread_id, success, processing))
        if success:
            print(f"Thread {thread_id}: Acquired lock, sleeping for 2 seconds...")
            time.sleep(2)
            release_file_locks(test_files)
            print(f"Thread {thread_id}: Released lock")
        else:
            print(f"Thread {thread_id}: Failed to acquire lock, files in progress: {processing}")
    
    # Start two threads trying to acquire the same lock
    thread1 = threading.Thread(target=try_acquire_lock, args=(1,))
    thread2 = threading.Thread(target=try_acquire_lock, args=(2,))
    
    thread1.start()
    time.sleep(0.1)  # Small delay to ensure thread1 starts first
    thread2.start()
    
    thread1.join()
    thread2.join()
    
    print(f"Results: {results}")
    
    # Verify that only one thread succeeded
    successful_threads = [r for r in results if r[1] == True]
    failed_threads = [r for r in results if r[1] == False]
    
    print(f"Successful threads: {len(successful_threads)}")
    print(f"Failed threads: {len(failed_threads)}")
    
    assert len(successful_threads) == 1, "Only one thread should succeed"
    assert len(failed_threads) == 1, "One thread should fail"
    
    print("Concurrent access test passed\n")

def test_multiple_files():
    """Test locking multiple files"""
    print("Testing multiple file locking...")
    
    test_files = ['multi1.xlsx', 'multi2.xlsx', 'multi3.xlsx']
    
    # Acquire locks on all files
    success, processing = acquire_file_locks(test_files)
    print(f"Multiple file lock acquisition: {success}")
    
    if success:
        # Try to acquire lock on subset (should fail)
        subset_success, subset_processing = acquire_file_locks(['multi1.xlsx'])
        print(f"Subset lock acquisition (should fail): {subset_success}, processing: {subset_processing}")
        
        # Release all locks
        release_file_locks(test_files)
        
        # Now subset should succeed
        subset_success2, subset_processing2 = acquire_file_locks(['multi1.xlsx'])
        print(f"Subset lock acquisition after release: {subset_success2}")
        
        if subset_success2:
            release_file_locks(['multi1.xlsx'])
    
    print("Multiple file locking test completed\n")

def cleanup_test_files():
    """Clean up any test files that might be stuck in processing state"""
    print("Cleaning up test files...")
    
    test_files = [
        'test_file1.xlsx', 'test_file2.xlsx', 
        'concurrent_test.xlsx', 
        'multi1.xlsx', 'multi2.xlsx', 'multi3.xlsx'
    ]
    
    for file in test_files:
        try:
            update_file_status([file], 'idle')
        except:
            pass  # Ignore errors for files that don't exist
    
    print("Cleanup completed")

if __name__ == "__main__":
    print("Starting status locking tests...\n")
    
    # Clean up any previous test state
    cleanup_test_files()
    
    try:
        # Run tests
        test_basic_locking()
        test_concurrent_access()
        test_multiple_files()
        
        print("All tests passed!")
        
    except Exception as e:
        print(f"Test failed with error: {e}")
        
    finally:
        # Clean up after tests
        cleanup_test_files()
